'use client'

import BackgroundCanvas from "@/components/ui/BackgroundCanvas";
import { useTheme } from "@/contexts/theme-context";
import ThemeToggle from "@/components/ui/ThemeToggle";
import Link from "next/link";

interface LayoutContentProps {
  children: React.ReactNode;
}

export default function LayoutContent({ children }: LayoutContentProps) {
  const { isDarkMode } = useTheme();

  return (
    <>
      {/* 3D星空背景 */}
      <BackgroundCanvas darkMode={isDarkMode} />
      
      <div className="relative z-10">
        <header className={`border-b shadow-sm sticky top-0 z-50 transition-all duration-300 ${
          isDarkMode 
            ? 'bg-black/20 backdrop-blur-md border-white/20' 
            : 'bg-white/20 backdrop-blur-md border-gray-200/50'
        }`}>
          <nav className="container mx-auto px-6 py-8">
            <div className="flex items-center">
              {/* 左侧 Logo */}
              <div className="flex-shrink-0">
                <Link
                  href="/"
                  className={`text-3xl font-bold transition-colors duration-300 ${
                    isDarkMode
                      ? 'text-white hover:text-purple-300'
                      : 'text-white hover:text-purple-300'  // 浅色模式也使用白色
                  }`}
                  style={{fontFamily: 'var(--font-noto-sans-sc)'}}
                >
                  我的博客
                </Link>
              </div>
              
              {/* 中间导航区域 */}
              <div className="flex-1 flex justify-center mx-16">
                <div className="flex items-center justify-between w-96">
                  <Link
                    href="/"
                    className={`relative text-xl font-semibold transition-all duration-300 py-3 px-6 rounded-xl group ${
                      isDarkMode
                        ? 'text-white/90 hover:text-purple-300 hover:bg-white/10'
                        : 'text-white/90 hover:text-purple-300 hover:bg-white/10'  // 浅色模式也使用白色
                    }`}
                    style={{fontFamily: 'var(--font-noto-sans-sc)'}}
                  >
                    首页
                    <span className="absolute bottom-1 left-1/2 transform -translate-x-1/2 w-0 h-0.5 bg-purple-400 transition-all duration-300 group-hover:w-3/4"></span>
                  </Link>
                  <Link
                    href="/blog"
                    className={`relative text-xl font-semibold transition-all duration-300 py-3 px-6 rounded-xl group ${
                      isDarkMode
                        ? 'text-white/90 hover:text-purple-300 hover:bg-white/10'
                        : 'text-white/90 hover:text-purple-300 hover:bg-white/10'  // 浅色模式也使用白色
                    }`}
                    style={{fontFamily: 'var(--font-noto-sans-sc)'}}
                  >
                    博客
                    <span className="absolute bottom-1 left-1/2 transform -translate-x-1/2 w-0 h-0.5 bg-purple-400 transition-all duration-300 group-hover:w-3/4"></span>
                  </Link>
                  <a 
                    href="/about" 
                    className={`relative text-xl font-semibold transition-all duration-300 py-3 px-6 rounded-xl group ${
                      isDarkMode 
                        ? 'text-white/90 hover:text-purple-300 hover:bg-white/10' 
                        : 'text-white/90 hover:text-purple-300 hover:bg-white/10'  // 浅色模式也使用白色
                    }`}
                    style={{fontFamily: 'var(--font-noto-sans-sc)'}}
                  >
                    关于
                    <span className="absolute bottom-1 left-1/2 transform -translate-x-1/2 w-0 h-0.5 bg-purple-400 transition-all duration-300 group-hover:w-3/4"></span>
                  </a>
                </div>
              </div>
              
              {/* 右侧主题切换按钮 */}
              <div className="flex-shrink-0 flex items-center justify-end w-32">
                <ThemeToggle />
              </div>
            </div>
          </nav>
        </header>
        
        <main className="min-h-screen relative z-10">
          {children}
        </main>
        
        <footer className={`border-t py-8 text-center transition-all duration-300 ${
          isDarkMode 
            ? 'border-white/20 text-white/60 bg-black/20 backdrop-blur-sm' 
            : 'border-white/20 text-white/60 bg-black/20 backdrop-blur-sm'  // 浅色模式也使用白色文字
        }`}>
          <p>&copy; 2025 我的个人博客. All rights reserved.</p>
        </footer>
      </div>
    </>
  );
}