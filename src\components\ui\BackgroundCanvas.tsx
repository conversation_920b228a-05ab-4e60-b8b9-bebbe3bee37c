'use client'

import { useEffect, useRef, useState } from 'react'
import * as THREE from 'three'

interface BackgroundCanvasProps {
  darkMode?: boolean
  mousePosition?: { x: number; y: number }
}

interface RippleEffect {
  position: THREE.Vector3
  radius: number
  maxRadius: number
  strength: number
  createdAt: number
  indicator?: THREE.Mesh  // 添加可视化指示器
}

export default function BackgroundCanvas({ darkMode = true, mousePosition }: BackgroundCanvasProps) {
  const canvasRef = useRef<HTMLCanvasElement>(null)
  const sceneRef = useRef<{
    scene: THREE.Scene
    camera: THREE.PerspectiveCamera
    renderer: THREE.WebGLRenderer
    stars: THREE.Points
    nebulae: THREE.Group
    animationId: number | null
    raycaster: THREE.Raycaster
  } | null>(null)

  const [isClient, setIsClient] = useState(false)
  const rippleEffectsRef = useRef<RippleEffect[]>([])
  const originalStarsPositions = useRef<Float32Array | null>(null)
  const scrollYRef = useRef(0)
  
  // 设备检测和性能优化
  const getDeviceSettings = () => {
    if (typeof window === 'undefined') {
      return { starsCount: 2000, pixelRatio: 1, antialias: false }
    }

    const isMobile = window.innerWidth <= 768
    const isLowPerformance = navigator.hardwareConcurrency <= 4 || 
                            (window.devicePixelRatio > 2 && isMobile)
    
    return {
      starsCount: isMobile ? (isLowPerformance ? 1000 : 2000) : 5000,
      pixelRatio: isMobile ? Math.min(window.devicePixelRatio, 1.5) : Math.min(window.devicePixelRatio, 2),
      antialias: !isMobile && !isLowPerformance,
      enableRipples: true, // 始终启用涟漪效果用于测试
      nebulaCount: isMobile ? 2 : 3,
      nebulaSize: isMobile ? 300 : 400
    }
  }

  useEffect(() => {
    setIsClient(true)
  }, [])

  useEffect(() => {
    if (!isClient || !canvasRef.current) return

    // 获取设备优化设置
    const deviceSettings = getDeviceSettings()

    // 初始化Three.js场景
    const scene = new THREE.Scene()
    
    // 根据主题设置场景背景色
    const updateSceneBackground = (isDark: boolean) => {
      if (isDark) {
        scene.background = new THREE.Color(0x000000) // 深黑色
      } else {
        scene.background = new THREE.Color(0x1a1a2e) // 深蓝灰色，保持星空感觉
      }
    }
    
    // 初始设置背景
    updateSceneBackground(darkMode)
    
    // 设置相机
    const camera = new THREE.PerspectiveCamera(
      75,
      window.innerWidth / window.innerHeight,
      0.1,
      2000
    )
    camera.position.z = 1000

    // 设置渲染器（根据设备性能调整）
    const renderer = new THREE.WebGLRenderer({
      canvas: canvasRef.current,
      alpha: true,
      antialias: deviceSettings.antialias, // 根据设备性能动态调整
    })
    renderer.setSize(window.innerWidth, window.innerHeight)
    renderer.setPixelRatio(deviceSettings.pixelRatio) // 动态像素比率

    // 创建星星粒子系统（根据设备性能调整数量）
    const starsGeometry = new THREE.BufferGeometry()
    const starsCount = deviceSettings.starsCount // 动态星星数量
    const starsPositions = new Float32Array(starsCount * 3)
    const starsOpacity = new Float32Array(starsCount)
    
    for (let i = 0; i < starsCount; i++) {
      // 随机分布在3D空间中
      starsPositions[i * 3] = (Math.random() - 0.5) * 2000
      starsPositions[i * 3 + 1] = (Math.random() - 0.5) * 2000
      starsPositions[i * 3 + 2] = (Math.random() - 0.5) * 2000
      
      // 随机初始透明度用于闪烁效果
      starsOpacity[i] = Math.random()
    }

    starsGeometry.setAttribute('position', new THREE.BufferAttribute(starsPositions, 3))
    starsGeometry.setAttribute('opacity', new THREE.BufferAttribute(starsOpacity, 1))

    // 保存原始星星位置用于涟漪效果
    originalStarsPositions.current = starsPositions.slice()

    // 星星材质
    const starsMaterial = new THREE.PointsMaterial({
      color: 0xffffff,
      size: 3,  // 增加星星大小
      sizeAttenuation: true,
      transparent: true,
      opacity: 0.9,  // 增加透明度
      blending: THREE.AdditiveBlending
    })

    const stars = new THREE.Points(starsGeometry, starsMaterial)
    scene.add(stars)

    // 创建星云效果（根据设备性能调整）
    const nebulae = new THREE.Group()
    
    // 动态星云数量和大小
    const nebulaColors = [
      new THREE.Color('#8B5CF6'), // 紫色
      new THREE.Color('#C084FC'), // 亮紫色  
      new THREE.Color('#F472B6')  // 粉色
    ]

    for (let i = 0; i < deviceSettings.nebulaCount; i++) {
      const nebulaGeometry = new THREE.PlaneGeometry(deviceSettings.nebulaSize, deviceSettings.nebulaSize)
      
      // 创建径向渐变纹理（移动设备使用较小纹理）
      const textureSize = deviceSettings.nebulaSize  >= 400 ? 256 : 128
      const canvas = document.createElement('canvas')
      canvas.width = textureSize
      canvas.height = textureSize
      const ctx = canvas.getContext('2d')!
      
      const gradient = ctx.createRadialGradient(textureSize/2, textureSize/2, 0, textureSize/2, textureSize/2, textureSize/2)
      const color = nebulaColors[i]
      gradient.addColorStop(0, `rgba(${Math.floor(color.r * 255)}, ${Math.floor(color.g * 255)}, ${Math.floor(color.b * 255)}, 0.8)`)
      gradient.addColorStop(0.5, `rgba(${Math.floor(color.r * 255)}, ${Math.floor(color.g * 255)}, ${Math.floor(color.b * 255)}, 0.4)`)
      gradient.addColorStop(1, `rgba(${Math.floor(color.r * 255)}, ${Math.floor(color.g * 255)}, ${Math.floor(color.b * 255)}, 0)`)
      
      ctx.fillStyle = gradient
      ctx.fillRect(0, 0, textureSize, textureSize)
      
      const texture = new THREE.CanvasTexture(canvas)
      
      const nebulaMaterial = new THREE.MeshBasicMaterial({
        map: texture,
        transparent: true,
        blending: THREE.AdditiveBlending,
        side: THREE.DoubleSide
      })
      
      const nebula = new THREE.Mesh(nebulaGeometry, nebulaMaterial)
      
      // 随机位置和旋转
      nebula.position.set(
        (Math.random() - 0.5) * 1000,
        (Math.random() - 0.5) * 1000,
        -800 - Math.random() * 200
      )
      nebula.rotation.z = Math.random() * Math.PI * 2
      
      nebulae.add(nebula)
    }
    
    scene.add(nebulae)

    // 创建射线投射器，用于精确的3D空间坐标转换
    const raycaster = new THREE.Raycaster()

    // 使用window级别的事件监听器（Three.js最佳实践）
    const handleWindowClick = (event: MouseEvent) => {
      console.log('Window click detected!', event.clientX, event.clientY) // 调试信息
      
      if (!sceneRef.current || !deviceSettings.enableRipples || !canvasRef.current) {
        return
      }

      // 检查点击是否在交互元素上（链接、按钮等）
      const target = event.target as Element
      if (target && (
        target.tagName === 'A' || 
        target.tagName === 'BUTTON' ||
        target.closest('a') ||
        target.closest('button') ||
        target.closest('nav') ||
        target.closest('header')
      )) {
        return
      }

      // 使用射线投射器获取准确的3D空间坐标
      const mouse = new THREE.Vector2()
      mouse.x = (event.clientX / window.innerWidth) * 2 - 1
      mouse.y = -(event.clientY / window.innerHeight) * 2 + 1
      
      // 更新射线投射器
      raycaster.setFromCamera(mouse, camera)
      
      // 创建一个虚拟平面用于计算交点
      const plane = new THREE.Plane(new THREE.Vector3(0, 0, 1), 0)
      const intersectionPoint = new THREE.Vector3()
      raycaster.ray.intersectPlane(plane, intersectionPoint)

      // 创建更明显的可视化指示器
      const indicatorGeometry = new THREE.RingGeometry(10, 80, 32) // 增大尺寸
      const indicatorMaterial = new THREE.MeshBasicMaterial({ 
        color: 0xff00ff, 
        transparent: true, 
        opacity: 0.8, // 增加不透明度
        side: THREE.DoubleSide
      })
      const indicator = new THREE.Mesh(indicatorGeometry, indicatorMaterial)
      indicator.position.copy(intersectionPoint)
      scene.add(indicator)

      // 创建涟漪效果 - 增加强度和范围
      const ripple: RippleEffect = {
        position: intersectionPoint,
        radius: 0,
        maxRadius: 800, // 增加最大半径
        strength: 500,  // 增加强度
        createdAt: Date.now(),
        indicator: indicator
      }
      
      rippleEffectsRef.current.push(ripple)
      console.log('Ripple created at:', intersectionPoint, 'Screen pos:', mouse.x.toFixed(2), mouse.y.toFixed(2))
    }

    // Three.js推荐的window级别事件监听
    window.addEventListener('click', handleWindowClick)

    // 滚动监听用于视差效果
    const handleScroll = () => {
      scrollYRef.current = window.scrollY
    }

    window.addEventListener('scroll', handleScroll, { passive: true })

    // 存储场景引用
    sceneRef.current = {
      scene,
      camera,
      renderer,
      stars,
      nebulae,
      animationId: null,
      raycaster // 保存raycaster引用
    }

    // 动画循环
    const animate = () => {
      if (!sceneRef.current) return

      const { scene, camera, renderer, stars, nebulae } = sceneRef.current
      
      // 获取时间用于一致的动画速度
      const time = Date.now() * 0.001 // 转换为秒
      
      // 视差滚动效果
      const scrollY = scrollYRef.current
      const parallaxOffset = scrollY * 0.0005
      
      // 星云效果 - 分离旋转和位置
      nebulae.position.y = parallaxOffset * 100
      // 保持恒定的旋转速度，不受滚动影响
      nebulae.rotation.y = time * 0.1 // 使用时间而非累积值
      
      // 星星视差效果（更慢移动）
      stars.position.y = parallaxOffset * 50
      // 星星也使用时间基础的旋转
      stars.rotation.y = time * 0.05 // 比星云慢一些
      
      // 处理涟漪效果（仅在支持的设备上）
      if (deviceSettings.enableRipples && rippleEffectsRef.current.length > 0) {
        const currentTime = Date.now()
        const positionsArray = stars.geometry.attributes.position.array as Float32Array
        const originalPositions = originalStarsPositions.current!
        
        // 重置所有星星到原始位置
        for (let i = 0; i < positionsArray.length; i++) {
          positionsArray[i] = originalPositions[i]
        }
        
        // 应用所有活跃的涟漪效果
        rippleEffectsRef.current = rippleEffectsRef.current.filter(ripple => {
          const elapsed = currentTime - ripple.createdAt
          const duration = 3000 // 延长到3秒生命周期
          
          if (elapsed > duration) {
            // 清理指示器
            if (ripple.indicator) {
              scene.remove(ripple.indicator)
              ripple.indicator.geometry.dispose()
              ripple.indicator.material.dispose()
            }
            return false
          }
          
          // 更新涟漪半径
          const progress = elapsed / duration
          ripple.radius = ripple.maxRadius * progress
          
          // 更新可视化指示器
          if (ripple.indicator) {
            const scale = 1 + progress * 15  // 指示器扩大更多
            ripple.indicator.scale.set(scale, scale, scale)
            ripple.indicator.material.opacity = 0.8 * (1 - progress)  // 逐渐透明
          }
          
          // 计算涟漪强度（渐弱效果）
          const strength = ripple.strength * (1 - progress)
          
          // 影响附近的星星 - 增强影响范围和强度
          let affectedStars = 0
          for (let i = 0; i < positionsArray.length / 3; i++) {
            const starPos = new THREE.Vector3(
              originalPositions[i * 3],
              originalPositions[i * 3 + 1],
              originalPositions[i * 3 + 2]
            )
            
            const distance = ripple.position.distanceTo(starPos)
            
            // 增加影响范围和强度
            if (distance < ripple.radius + 200) {
              // 使用更陡峭的衰减曲线，使效果更明显
              const maxInfluenceDistance = ripple.radius + 200
              const influenceFactor = 1 - Math.pow(distance / maxInfluenceDistance, 2) // 平方衰减
              const influence = influenceFactor * strength * 5 // 增加整体影响倍数
              
              if (influence > 0.5) { // 只对有明显影响的星星进行处理
                const direction = starPos.clone().sub(ripple.position).normalize()
                
                positionsArray[i * 3] += direction.x * influence
                positionsArray[i * 3 + 1] += direction.y * influence
                positionsArray[i * 3 + 2] += direction.z * influence
                
                affectedStars++
              }
            }
          }
          
          if (affectedStars > 0 && elapsed < 100) { // 只在开始时打印一次
            console.log(`Ripple affecting ${affectedStars} stars at radius ${ripple.radius.toFixed(1)}`)
          }
          
          return true
        })
        
        stars.geometry.attributes.position.needsUpdate = true
      }
      
      // 星星闪烁效果
      const opacityArray = stars.geometry.attributes.opacity.array as Float32Array
      for (let i = 0; i < opacityArray.length; i++) {
        opacityArray[i] += (Math.random() - 0.5) * 0.02
        opacityArray[i] = Math.max(0.1, Math.min(1, opacityArray[i]))
      }
      stars.geometry.attributes.opacity.needsUpdate = true

      renderer.render(scene, camera)
      sceneRef.current.animationId = requestAnimationFrame(animate)
    }

    animate()

    // 窗口尺寸变化处理
    const handleResize = () => {
      if (!sceneRef.current) return
      
      const { camera, renderer } = sceneRef.current
      camera.aspect = window.innerWidth / window.innerHeight
      camera.updateProjectionMatrix()
      renderer.setSize(window.innerWidth, window.innerHeight)
    }

    window.addEventListener('resize', handleResize)

    // 清理函数
    return () => {
      window.removeEventListener('resize', handleResize)
      window.removeEventListener('scroll', handleScroll)
      window.removeEventListener('click', handleWindowClick)
      
      if (sceneRef.current) {
        if (sceneRef.current.animationId) {
          cancelAnimationFrame(sceneRef.current.animationId)
        }
        
        sceneRef.current.renderer.dispose()
        sceneRef.current.scene.clear()
      }
    }
  }, [isClient])

  // 处理主题变化
  useEffect(() => {
    if (!sceneRef.current) return

    const { scene, stars, nebulae } = sceneRef.current
    
    // 更新场景背景色
    if (darkMode) {
      scene.background = new THREE.Color(0x000000) // 深黑色
    } else {
      scene.background = new THREE.Color(0x1a1a2e) // 深蓝灰色，保持星空感觉
    }
    
    // 更新星星透明度
    const opacity = darkMode ? 0.8 : 1.0  // 浅色模式下提高星星亮度
    stars.material.opacity = opacity
    
    // 更新星云透明度
    nebulae.children.forEach((nebula) => {
      if (nebula instanceof THREE.Mesh) {
        nebula.material.opacity = darkMode ? 0.8 : 1.0  // 浅色模式下提高星云亮度
      }
    })
  }, [darkMode])

  if (!isClient) {
    return null // 服务端渲染时不显示
  }

  return (
    <canvas
      ref={canvasRef}
      className="fixed inset-0 w-full h-full cursor-default touch-none select-none"
      style={{ 
        zIndex: -1,  // 保持在背景
        touchAction: 'none', // 防止移动设备滚动冲突
        userSelect: 'none'   // 防止文本选择
      }}
    />
  )
}