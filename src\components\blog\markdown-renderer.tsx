'use client'

import ReactMarkdown from 'react-markdown'
import { Prism as Syntax<PERSON>ighlighter } from 'react-syntax-highlighter'
import { vscDarkPlus } from 'react-syntax-highlighter/dist/esm/styles/prism'
import { useTheme } from '@/contexts/theme-context'
import { useEffect, useState } from 'react'

interface MarkdownRendererProps {
  content: string
}

export default function MarkdownRenderer({ content }: MarkdownRendererProps) {
  const { isDarkMode } = useTheme()
  const [mounted, setMounted] = useState(false)

  useEffect(() => {
    setMounted(true)
  }, [])

  // 防止水合错误
  if (!mounted) {
    return <div className="animate-pulse">Loading...</div>
  }

  return (
    <div className="max-w-none">
      <ReactMarkdown
        components={{
          code({ className, children }) {
            const match = /language-(\w+)/.exec(className || '')
            const language = match ? match[1] : ''
            
            if (language) {
              return (
                <SyntaxHighlighter
                  style={vscDarkPlus as { [key: string]: React.CSSProperties }}
                  language={language}
                  PreTag="div"
                  className="rounded-lg !bg-gray-900 !m-0"

                >
                  {String(children).replace(/\n$/, '')}
                </SyntaxHighlighter>
              )
            }
            
            return (
              <code 
                className={`px-2 py-1 rounded text-sm font-mono ${
                  isDarkMode 
                    ? 'bg-gray-800 text-gray-300' 
                    : 'bg-gray-700 text-white'
                }`} 

              >
                {children}
              </code>
            )
          },
          // 自定义其他组件以支持深色主题
          h1: ({ children }) => (
            <h1 className={`text-3xl font-bold mt-8 mb-6 ${isDarkMode ? 'text-white' : 'text-white'}`}>
              {children}
            </h1>
          ),
          h2: ({ children }) => (
            <h2 className={`text-2xl font-bold mt-8 mb-4 ${isDarkMode ? 'text-white' : 'text-white'}`}>
              {children}
            </h2>
          ),
          h3: ({ children }) => (
            <h3 className={`text-xl font-semibold mt-6 mb-3 ${isDarkMode ? 'text-white' : 'text-white'}`}>
              {children}
            </h3>
          ),
          h4: ({ children }) => (
            <h4 className={`text-lg font-semibold mt-4 mb-2 ${isDarkMode ? 'text-white' : 'text-white'}`}>
              {children}
            </h4>
          ),
          h5: ({ children }) => (
            <h5 className={`text-base font-semibold mt-4 mb-2 ${isDarkMode ? 'text-white' : 'text-white'}`}>
              {children}
            </h5>
          ),
          h6: ({ children }) => (
            <h6 className={`text-sm font-semibold mt-4 mb-2 ${isDarkMode ? 'text-white' : 'text-white'}`}>
              {children}
            </h6>
          ),
          p: ({ children }) => (
            <p className={`mb-4 leading-7 ${isDarkMode ? 'text-gray-300' : 'text-white'}`}>
              {children}
            </p>
          ),
          ul: ({ children }) => (
            <ul className={`my-4 ml-6 list-disc ${isDarkMode ? 'text-gray-300' : 'text-white'}`}>
              {children}
            </ul>
          ),
          ol: ({ children }) => (
            <ol className={`my-4 ml-6 list-decimal ${isDarkMode ? 'text-gray-300' : 'text-white'}`}>
              {children}
            </ol>
          ),
          li: ({ children }) => (
            <li className="mb-1">
              {children}
            </li>
          ),
          blockquote: ({ children }) => (
            <blockquote className={`border-l-4 pl-4 my-4 italic ${
              isDarkMode 
                ? 'border-gray-600 text-gray-400' 
                : 'border-gray-400 text-gray-200'
            }`}>
              {children}
            </blockquote>
          ),
          a: ({ children, href }) => (
            <a 
              href={href}
              className={`underline hover:no-underline ${
                isDarkMode 
                  ? 'text-blue-400 hover:text-blue-300' 
                  : 'text-blue-300 hover:text-blue-200'
              }`}
              target={href?.startsWith('http') ? '_blank' : undefined}
              rel={href?.startsWith('http') ? 'noopener noreferrer' : undefined}
            >
              {children}
            </a>
          ),
          table: ({ children }) => (
            <div className="overflow-x-auto my-4">
              <table className={`min-w-full border-collapse ${
                isDarkMode ? 'border-gray-600' : 'border-gray-400'
              }`}>
                {children}
              </table>
            </div>
          ),
          th: ({ children }) => (
            <th className={`border px-4 py-2 text-left font-semibold ${
              isDarkMode 
                ? 'border-gray-600 bg-gray-800 text-white' 
                : 'border-gray-400 bg-gray-700 text-white'
            }`}>
              {children}
            </th>
          ),
          td: ({ children }) => (
            <td className={`border px-4 py-2 ${
              isDarkMode 
                ? 'border-gray-600 text-gray-300' 
                : 'border-gray-400 text-white'
            }`}>
              {children}
            </td>
          ),
          strong: ({ children }) => (
            <strong className={`font-bold ${isDarkMode ? 'text-white' : 'text-white'}`}>
              {children}
            </strong>
          ),
          em: ({ children }) => (
            <em className={`italic ${isDarkMode ? 'text-gray-300' : 'text-white'}`}>
              {children}
            </em>
          ),
          hr: () => (
            <hr className={`my-8 ${isDarkMode ? 'border-gray-600' : 'border-gray-300'}`} />
          ),
        }}
      >
        {content}
      </ReactMarkdown>
    </div>
  )
}