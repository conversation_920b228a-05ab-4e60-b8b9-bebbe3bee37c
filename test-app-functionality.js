// 测试应用功能
const { getPublishedPosts, getFeaturedPosts, getPostBySlug } = require('./src/lib/posts');

async function testAppFunctionality() {
  console.log('🧪 开始测试应用功能...\n');
  
  try {
    // 测试1: 获取已发布文章
    console.log('📝 1. 测试获取已发布文章:');
    const publishedPosts = await getPublishedPosts();
    console.log(`   ✅ 成功获取 ${publishedPosts.length} 篇已发布文章`);
    
    if (publishedPosts.length > 0) {
      const firstPost = publishedPosts[0];
      console.log(`   📄 第一篇文章: "${firstPost.title}" (${firstPost.slug})`);
    }
    
    // 测试2: 获取精选文章
    console.log('\n⭐ 2. 测试获取精选文章:');
    const featuredPosts = await getFeaturedPosts();
    console.log(`   ✅ 成功获取 ${featuredPosts.length} 篇精选文章`);
    
    // 测试3: 根据slug获取文章
    if (publishedPosts.length > 0) {
      console.log('\n🔍 3. 测试根据slug获取文章:');
      const firstPostSlug = publishedPosts[0].slug;
      const postBySlug = await getPostBySlug(firstPostSlug);
      
      if (postBySlug) {
        console.log(`   ✅ 成功获取文章: "${postBySlug.title}"`);
        console.log(`   📊 文章详情:`);
        console.log(`      - ID: ${postBySlug.id}`);
        console.log(`      - 标题: ${postBySlug.title}`);
        console.log(`      - Slug: ${postBySlug.slug}`);
        console.log(`      - 发布状态: ${postBySlug.published ? '已发布' : '草稿'}`);
        console.log(`      - 精选状态: ${postBySlug.isFeatured ? '是' : '否'}`);
        console.log(`      - 摘要: ${postBySlug.excerpt || '无摘要'}`);
        console.log(`      - 内容长度: ${postBySlug.content.length} 字符`);
      } else {
        console.log(`   ❌ 无法获取文章: ${firstPostSlug}`);
      }
    }
    
    console.log('\n🎉 应用功能测试完成！');
    console.log('\n✅ 测试结果总结:');
    console.log(`   - 数据库连接: 正常`);
    console.log(`   - 文章查询功能: 正常`);
    console.log(`   - 数据完整性: 正常`);
    console.log(`   - 应用核心功能: 可用`);
    
    console.log('\n🚀 您的博客应用已准备就绪！');
    console.log('   可以访问以下页面:');
    console.log('   - 首页: http://localhost:3000/');
    console.log('   - 博客列表: http://localhost:3000/blog');
    if (publishedPosts.length > 0) {
      console.log(`   - 示例文章: http://localhost:3000/blog/${publishedPosts[0].slug}`);
    }
    console.log('   - 管理后台: http://localhost:3000/admin');
    
  } catch (error) {
    console.error('❌ 应用功能测试失败:', error.message);
    console.error('详细错误:', error);
    process.exit(1);
  }
}

testAppFunctionality();
