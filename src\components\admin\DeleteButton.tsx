'use client'

import { useState } from 'react'
import { deletePostWithConfirmAction } from '@/lib/actions'

interface DeleteButtonProps {
  postId: number
  postTitle: string
}

export default function DeleteButton({ postId, postTitle }: DeleteButtonProps) {
  const [isDeleting, setIsDeleting] = useState(false)

  const handleDelete = async () => {
    if (!confirm(`确定要删除文章《${postTitle}》吗？此操作不可恢复。`)) {
      return
    }

    setIsDeleting(true)
    try {
      await deletePostWithConfirmAction(postId)
    } catch (error) {
      console.error('删除失败:', error)
      alert('删除失败，请重试')
    } finally {
      setIsDeleting(false)
    }
  }

  return (
    <button
      type="button"
      onClick={handleDelete}
      disabled={isDeleting}
      className="text-red-600 hover:text-red-900 disabled:opacity-50"
    >
      {isDeleting ? '删除中...' : '删除'}
    </button>
  )
}