import { NextRequest, NextResponse } from 'next/server'
import { isAuthenticated } from '@/lib/auth'

export async function middleware(request: NextRequest) {
  const { pathname } = request.nextUrl

  // 保护 /admin 路径，但排除登录页面
  if (pathname.startsWith('/admin') && !pathname.startsWith('/admin/login')) {
    const authenticated = await isAuthenticated()
    
    if (!authenticated) {
      // 重定向到登录页面
      const loginUrl = new URL('/admin/login', request.url)
      return NextResponse.redirect(loginUrl)
    }
  }

  // 如果已经登录，访问登录页面时重定向到管理后台
  if (pathname === '/admin/login') {
    const authenticated = await isAuthenticated()
    
    if (authenticated) {
      const adminUrl = new URL('/admin', request.url)
      return NextResponse.redirect(adminUrl)
    }
  }

  return NextResponse.next()
}

export const config = {
  matcher: [
    '/admin/:path*'
  ]
}