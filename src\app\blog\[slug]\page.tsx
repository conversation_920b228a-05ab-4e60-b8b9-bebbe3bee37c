import { Metadata } from 'next'
import { notFound } from 'next/navigation'
import { getPostBySlug, getPublishedPosts } from '@/lib/posts'
import { format } from 'date-fns'
import { zhCN } from 'date-fns/locale'
import MarkdownRenderer from '@/components/blog/markdown-renderer'

interface BlogPostProps {
  params: Promise<{
    slug: string
  }>
}

export async function generateStaticParams() {
  const posts = await getPublishedPosts()
  return posts.map((post) => ({
    slug: post.slug,
  }))
}

export async function generateMetadata({ params }: BlogPostProps): Promise<Metadata> {
  const resolvedParams = await params
  const post = await getPostBySlug(resolvedParams.slug)

  if (!post) {
    return {
      title: '文章未找到',
      description: '请求的文章不存在',
    }
  }

  return {
    title: `${post.title} - 我的博客`,
    description: post.excerpt || '博客文章详情页',
  }
}

export default async function BlogPost({ params }: BlogPostProps) {
  const resolvedParams = await params
  const post = await getPostBySlug(resolvedParams.slug)

  if (!post) {
    notFound()
  }

  return (
    <div className="container mx-auto px-4 py-8 max-w-4xl">
      <article className="mx-auto">
        {/* 文章头部 */}
        <header className="mb-8 pb-8 border-b border-gray-200 dark:border-gray-700">
          <h1 className="text-4xl font-bold mb-6 text-gray-900 dark:text-white leading-tight">
            {post.title}
          </h1>
          <div className="flex items-center text-gray-600 dark:text-gray-400 space-x-4">
            {post.createdAt && (
              <time className="flex items-center space-x-2">
                <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z" clipRule="evenodd" />
                </svg>
                <span>
                  {format(new Date(post.createdAt), 'yyyy年MM月dd日', { locale: zhCN })}
                </span>
              </time>
            )}
            <span className="flex items-center space-x-2">
              <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clipRule="evenodd" />
              </svg>
              <span>5分钟阅读</span>
            </span>
            {post.isFeatured && (
              <span className="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-gradient-to-r from-yellow-400 to-orange-500 text-white">
                <span className="mr-1">⭐</span>
                精选文章
              </span>
            )}
          </div>
          {post.excerpt && (
            <p className="mt-4 text-lg text-gray-600 dark:text-gray-300 leading-relaxed">
              {post.excerpt}
            </p>
          )}
        </header>

        {/* 文章内容 */}
        <div className="article-content">
          <MarkdownRenderer content={post.content} />
        </div>

        {/* 文章底部 */}
        <footer className="mt-12 pt-8 border-t border-gray-200 dark:border-gray-700">
          <div className="flex items-center justify-between">
            <div className="text-sm text-gray-500 dark:text-gray-400">
              最后更新：
              {post.updatedAt && (
                <time className="ml-1">
                  {format(new Date(post.updatedAt), 'yyyy年MM月dd日', { locale: zhCN })}
                </time>
              )}
            </div>
            <div className="flex space-x-4">
              <a 
                href="/blog" 
                className="inline-flex items-center space-x-2 text-purple-600 dark:text-purple-400 hover:text-purple-500 dark:hover:text-purple-300 transition-colors"
              >
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
                </svg>
                <span>返回博客</span>
              </a>
            </div>
          </div>
        </footer>
      </article>
    </div>
  )
}