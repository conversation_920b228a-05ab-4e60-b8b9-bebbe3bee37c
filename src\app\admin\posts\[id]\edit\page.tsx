import { 
  getPostByIdForAdmin, 
  getAllTags, 
  getPostTags
} from '@/lib/posts'
import EditPostForm from '@/components/admin/EditPostForm'
import { notFound } from 'next/navigation'

interface EditPostPageProps {
  params: {
    id: string
  }
}

export default async function EditPostPage({ params }: EditPostPageProps) {
  const postId = parseInt(params.id)
  
  if (isNaN(postId)) {
    notFound()
  }

  try {
    // 并行加载文章和标签数据
    const [post, tags, postTags] = await Promise.all([
      getPostByIdForAdmin(postId),
      getAllTags(),
      getPostTags(postId)
    ])

    if (!post) {
      notFound()
    }

    return (
      <div className="min-h-screen bg-gray-50">
        <div className="max-w-4xl mx-auto py-6 sm:px-6 lg:px-8">
          <div className="px-4 py-6 sm:px-0">
            <div className="mb-8">
              <h1 className="text-2xl font-bold text-gray-900">编辑文章</h1>
              <p className="mt-2 text-sm text-gray-700">
                修改文章信息并保存
              </p>
            </div>
            
            <EditPostForm 
              post={post}
              tags={tags}
              postTags={postTags}
            />
          </div>
        </div>
      </div>
    )
  } catch (error) {
    console.error('加载文章数据失败:', error)
    notFound()
  }
}