﻿import { drizzle } from 'drizzle-orm/mysql2'
import mysql from 'mysql2/promise'
import * as schema from '@/db/schema'

const connection = mysql.createPool({
  host: 'gateway01.ap-northeast-1.prod.aws.tidbcloud.com',
  port: 4000,
  user: '2oERGgZyGpnieY9.root',
  password: 'csMQAt3HshS7CVFH',
  database: 'blog_db',
  ssl: {
    rejectUnauthorized: false
  }
})

export const db = drizzle(connection, { schema, mode: 'default' })
