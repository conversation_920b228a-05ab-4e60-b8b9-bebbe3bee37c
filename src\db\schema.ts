﻿import { mysqlTable, int, text, timestamp, boolean } from 'drizzle-orm/mysql-core'

export const posts = mysqlTable('posts', {
  id: int('id').primaryKey().autoincrement(),
  title: text('title').notNull(),
  slug: text('slug').notNull().unique(),
  content: text('content').notNull(),
  excerpt: text('excerpt'),
  published: boolean('published').default(false),
  isFeatured: boolean('is_featured').default(false), // <-- 新增字段
  createdAt: timestamp('created_at').defaultNow(),
  updatedAt: timestamp('updated_at').defaultNow(),
})

export const tags = mysqlTable('tags', {
  id: int('id').primaryKey().autoincrement(),
  name: text('name').notNull().unique(),
  slug: text('slug').notNull().unique(),
})

export const postTags = mysqlTable('post_tags', {
  id: int('id').primaryKey().autoincrement(),
  postId: int('post_id').references(() => posts.id),
  tagId: int('tag_id').references(() => tags.id),
})
