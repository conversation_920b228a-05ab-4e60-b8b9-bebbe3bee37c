@import "tailwindcss";

:root {
  --background: #1a1a2e;
  --foreground: #ffffff;
}

[data-theme="dark"] {
  --background: #0a0a0a;
  --foreground: #ededed;
}

[data-theme="light"] {
  --background: #1a1a2e;
  --foreground: #ffffff;
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
  --font-noto-sans-sc: var(--font-noto-sans-sc);
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: #0a0a0a;
    --foreground: #ededed;
  }
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: var(--font-noto-sans-sc), var(--font-geist-sans), Arial, Helvetica, sans-serif;
  transition: background-color 0.3s ease, color 0.3s ease;
}

/* 确保页面内容在主题切换时平滑过渡 */
* {
  transition: background-color 0.3s ease, color 0.3s ease, border-color 0.3s ease;
}
