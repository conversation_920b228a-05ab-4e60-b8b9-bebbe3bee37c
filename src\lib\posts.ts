import { db } from './db'
import { posts, tags, postTags } from '@/db/schema'
import { eq, desc, and, like, count, sql, or } from 'drizzle-orm'

export interface Post {
  id: number
  title: string
  slug: string
  content: string
  excerpt?: string | null
  published: boolean
  isFeatured: boolean
  createdAt: Date | null
  updatedAt: Date | null
  tags?: Tag[]
}

export interface PostsListResult {
  posts: Post[]
  total: number
  totalPages: number
}

export interface Tag {
  id: number
  name: string
  slug: string
}

// 获取所有已发布的文章
export async function getPublishedPosts(limit?: number): Promise<Post[]> {
  try {
    let query = db
      .select()
      .from(posts)
      .where(eq(posts.published, true))
      .orderBy(desc(posts.createdAt))
    
    if (limit) {
      query = query.limit(limit)
    }
    
    const result = await query
    return result
  } catch (error) {
    console.error('Error fetching posts:', error)
    return []
  }
}

// 获取已发布的精选文章
export async function getFeaturedPosts(limit?: number): Promise<Post[]> {
  try {
    let query = db
      .select()
      .from(posts)
      .where(and(eq(posts.published, true), eq(posts.isFeatured, true)))
      .orderBy(desc(posts.createdAt))
    
    if (limit) {
      query = query.limit(limit)
    }
    
    const result = await query
    return result
  } catch (error) {
    console.error('Error fetching featured posts:', error)
    return []
  }
}

// 根据 slug 获取文章
export async function getPostBySlug(slug: string): Promise<Post | null> {
  try {
    const result = await db
      .select()
      .from(posts)
      .where(and(eq(posts.slug, slug), eq(posts.published, true)))
      .limit(1)
    
    return result[0] || null
  } catch (error) {
    console.error('Error fetching post by slug:', error)
    return null
  }
}

// 创建新文章
export async function createPost(postData: {
  title: string
  slug: string
  content: string
  excerpt?: string
  published?: boolean
  isFeatured?: boolean
}): Promise<Post | null> {
  try {
    const result = await db
      .insert(posts)
      .values({
        ...postData,
        published: postData.published ?? false,
        isFeatured: postData.isFeatured ?? false,
        createdAt: new Date(),
        updatedAt: new Date(),
      })
      .returning()
    
    return result[0] || null
  } catch (error) {
    console.error('创建文章失败:', error)
    return null
  }
}

// 获取所有标签
export async function getAllTags(): Promise<Tag[]> {
  try {
    const result = await db.select().from(tags)
    return result
  } catch (error) {
    console.error('获取标签失败:', error)
    return []
  }
}

// 管理后台专用函数

// 获取所有文章（管理后台用，包括未发布的）
export async function getAllPostsForAdmin(
  page: number = 1,
  limit: number = 10,
  search?: string,
  status?: 'all' | 'published' | 'draft' | 'featured'
): Promise<PostsListResult> {
  try {
    const offset = (page - 1) * limit
    
    // 构建查询条件
    const conditions = []
    
    if (search) {
      conditions.push(
        or(
          like(posts.title, `%${search}%`),
          like(posts.content, `%${search}%`)
        )
      )
    }
    
    if (status === 'published') {
      conditions.push(eq(posts.published, true))
    } else if (status === 'draft') {
      conditions.push(eq(posts.published, false))
    } else if (status === 'featured') {
      conditions.push(eq(posts.isFeatured, true))
    }
    
    const whereClause = conditions.length > 0 ? and(...conditions) : undefined
    
    // 获取文章列表
    const postsResult = await db
      .select()
      .from(posts)
      .where(whereClause)
      .orderBy(desc(posts.updatedAt))
      .limit(limit)
      .offset(offset)
    
    // 获取总数
    const countResult = await db
      .select({ count: count() })
      .from(posts)
      .where(whereClause)
    
    const total = countResult[0]?.count || 0
    const totalPages = Math.ceil(total / limit)
    
    return {
      posts: postsResult,
      total,
      totalPages
    }
  } catch (error) {
    console.error('获取文章列表失败:', error)
    return {
      posts: [],
      total: 0,
      totalPages: 0
    }
  }
}

// 根据ID获取文章（管理后台用）
export async function getPostByIdForAdmin(id: number): Promise<Post | null> {
  try {
    const result = await db
      .select()
      .from(posts)
      .where(eq(posts.id, id))
      .limit(1)
    
    return result[0] || null
  } catch (error) {
    console.error('获取文章失败:', error)
    return null
  }
}

// 更新文章
export async function updatePost(
  id: number,
  postData: {
    title?: string
    slug?: string
    content?: string
    excerpt?: string
    published?: boolean
    isFeatured?: boolean
  }
): Promise<Post | null> {
  try {
    const result = await db
      .update(posts)
      .set({
        ...postData,
        updatedAt: new Date(),
      })
      .where(eq(posts.id, id))
      .returning()
    
    return result[0] || null
  } catch (error) {
    console.error('更新文章失败:', error)
    return null
  }
}

// 删除文章
export async function deletePost(id: number): Promise<boolean> {
  try {
    // 首先删除文章标签关联
    await db.delete(postTags).where(eq(postTags.postId, id))
    
    // 然后删除文章
    const result = await db.delete(posts).where(eq(posts.id, id))
    
    return true
  } catch (error) {
    console.error('删除文章失败:', error)
    return false
  }
}

// 切换文章发布状态
export async function togglePostStatus(id: number): Promise<Post | null> {
  try {
    // 首先获取当前状态
    const currentPost = await getPostByIdForAdmin(id)
    if (!currentPost) return null
    
    // 切换状态
    const result = await db
      .update(posts)
      .set({
        published: !currentPost.published,
        updatedAt: new Date(),
      })
      .where(eq(posts.id, id))
      .returning()
    
    return result[0] || null
  } catch (error) {
    console.error('切换文章状态失败:', error)
    return null
  }
}

// 切换文章精选状态
export async function togglePostFeatured(id: number): Promise<Post | null> {
  try {
    // 首先获取当前状态
    const currentPost = await getPostByIdForAdmin(id)
    if (!currentPost) return null
    
    // 切换精选状态
    const result = await db
      .update(posts)
      .set({
        isFeatured: !currentPost.isFeatured,
        updatedAt: new Date(),
      })
      .where(eq(posts.id, id))
      .returning()
    
    return result[0] || null
  } catch (error) {
    console.error('切换文章精选状态失败:', error)
    return null
  }
}

// 从标题生成 slug
export function generateSlug(title: string): string {
  return title
    .toLowerCase()
    .trim()
    // 替换中文字符和特殊字符为连字符
    .replace(/[\u4e00-\u9fa5\s]+/g, '-')
    .replace(/[^\w\-]+/g, '-')
    .replace(/\-\-+/g, '-')
    .replace(/^-+/, '')
    .replace(/-+$/, '')
    .substring(0, 50) // 限制长度
}

// 检查 slug 是否已存在
export async function isSlugExists(slug: string, excludeId?: number): Promise<boolean> {
  try {
    const conditions = [eq(posts.slug, slug)]
    if (excludeId) {
      conditions.push(sql`${posts.id} != ${excludeId}`)
    }
    
    const result = await db
      .select({ id: posts.id })
      .from(posts)
      .where(and(...conditions))
      .limit(1)
    
    return result.length > 0
  } catch (error) {
    console.error('检查slug失败:', error)
    return false
  }
}

// 获取文章的标签
export async function getPostTags(postId: number): Promise<Tag[]> {
  try {
    const result = await db
      .select({
        id: tags.id,
        name: tags.name,
        slug: tags.slug,
      })
      .from(postTags)
      .innerJoin(tags, eq(postTags.tagId, tags.id))
      .where(eq(postTags.postId, postId))
    
    return result
  } catch (error) {
    console.error('获取文章标签失败:', error)
    return []
  }
}

// 更新文章标签关联
export async function updatePostTags(postId: number, tagIds: number[]): Promise<boolean> {
  try {
    // 删除现有关联
    await db.delete(postTags).where(eq(postTags.postId, postId))
    
    // 添加新关联
    if (tagIds.length > 0) {
      const tagRelations = tagIds.map(tagId => ({
        postId,
        tagId,
      }))
      
      await db.insert(postTags).values(tagRelations)
    }
    
    return true
  } catch (error) {
    console.error('更新文章标签失败:', error)
    return false
  }
}

// 创建新标签
export async function createTag(tagData: {
  name: string
  slug: string
}): Promise<Tag | null> {
  try {
    const result = await db
      .insert(tags)
      .values(tagData)
      .returning()
    
    return result[0] || null
  } catch (error) {
    console.error('创建标签失败:', error)
    return null
  }
}