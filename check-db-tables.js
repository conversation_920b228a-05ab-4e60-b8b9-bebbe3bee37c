// 检查数据库中的现有表
const mysql = require('mysql2/promise');

async function checkTables() {
  console.log('检查数据库中的现有表...');
  
  try {
    const connection = await mysql.createConnection({
      host: 'gateway01.ap-northeast-1.prod.aws.tidbcloud.com',
      port: 4000,
      user: '2oERGgZyGpnieY9.root',
      password: 'csMQAt3HshS7CVFH',
      database: 'blog_db',
      ssl: {
        rejectUnauthorized: false
      }
    });

    console.log('✅ 数据库连接成功！');
    
    // 查看所有表
    const [tables] = await connection.execute('SHOW TABLES');
    console.log('📋 现有表:', tables);
    
    // 如果有表，查看表结构
    for (const table of tables) {
      const tableName = Object.values(table)[0];
      console.log(`\n📊 表 ${tableName} 的结构:`);
      const [columns] = await connection.execute(`DESCRIBE ${tableName}`);
      console.table(columns);
    }
    
    await connection.end();
    console.log('✅ 检查完成');
    
  } catch (error) {
    console.error('❌ 检查失败:', error.message);
  }
}

checkTables();
