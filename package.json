{"name": "my-awesome-blog", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "db:generate": "drizzle-kit generate", "db:push": "drizzle-kit push", "db:studio": "drizzle-kit studio", "db:seed": "tsx scripts/seed.ts"}, "dependencies": {"@auth/drizzle-adapter": "^1.10.0", "@mdx-js/loader": "^3.1.0", "@mdx-js/react": "^3.1.0", "@types/react-syntax-highlighter": "^15.5.13", "@types/three": "^0.178.1", "@uiw/react-md-editor": "^4.0.8", "clsx": "^2.1.1", "date-fns": "^4.1.0", "dotenv": "^17.2.1", "drizzle-orm": "^0.44.4", "gray-matter": "^4.0.3", "jose": "^6.0.12", "lucide-react": "^0.534.0", "mysql2": "^3.14.3", "next": "15.4.5", "react": "19.1.0", "react-dom": "19.1.0", "react-markdown": "^10.1.0", "react-syntax-highlighter": "^15.6.1", "reading-time": "^1.5.0", "tailwind-merge": "^3.3.1", "three": "^0.178.0", "zod": "^4.0.14"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/mdx": "^2.0.13", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "drizzle-kit": "^0.31.4", "eslint": "^9", "eslint-config-next": "15.4.5", "prettier": "^3.6.2", "prettier-plugin-tailwindcss": "^0.6.14", "rehype-highlight": "^7.0.2", "rehype-slug": "^6.0.0", "remark-gfm": "^4.0.1", "tailwindcss": "^4", "tsx": "^4.20.3", "typescript": "^5"}}