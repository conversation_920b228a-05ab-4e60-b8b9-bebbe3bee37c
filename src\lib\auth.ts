import { SignJWT, jwtVerify } from 'jose'
import { cookies } from 'next/headers'

const secret = new TextEncoder().encode(process.env.JWT_SECRET!)
const alg = 'HS256'

export interface SessionPayload {
  username: string
  isAdmin: boolean
  iat: number
  exp: number
}

export interface LoginCredentials {
  username: string
  password: string
}

export async function validateCredentials(credentials: LoginCredentials): Promise<boolean> {
  const { username, password } = credentials
  
  return (
    username === process.env.ADMIN_USERNAME &&
    password === process.env.ADMIN_PASSWORD
  )
}

export async function generateToken(username: string): Promise<string> {
  const expiresIn = process.env.JWT_EXPIRES_IN || '24h'
  
  const jwt = await new SignJWT({ 
    username,
    isAdmin: true 
  })
    .setProtectedHeader({ alg })
    .setIssuedAt()
    .setIssuer('blog-admin')
    .setAudience('blog-admin')
    .setExpirationTime(expiresIn)
    .sign(secret)

  return jwt
}

export async function verifyToken(token: string): Promise<SessionPayload | null> {
  try {
    const { payload } = await jwtVerify(token, secret, {
      issuer: 'blog-admin',
      audience: 'blog-admin',
    })

    return payload as unknown as SessionPayload
  } catch (error) {
    console.error('令牌验证失败:', error)
    return null
  }
}

export async function setAuthCookie(token: string): Promise<void> {
  const cookieStore = await cookies()
  
  cookieStore.set('auth-token', token, {
    httpOnly: true,
    secure: process.env.NODE_ENV === 'production',
    sameSite: 'lax',
    maxAge: 24 * 60 * 60, // 24小时
    path: '/',
  })
}

export async function getAuthToken(): Promise<string | undefined> {
  const cookieStore = await cookies()
  return cookieStore.get('auth-token')?.value
}

export async function clearAuthCookie(): Promise<void> {
  const cookieStore = await cookies()
  
  cookieStore.set('auth-token', '', {
    httpOnly: true,
    secure: process.env.NODE_ENV === 'production',
    sameSite: 'lax',
    maxAge: 0,
    path: '/',
  })
}

export async function getCurrentUser(): Promise<SessionPayload | null> {
  const token = await getAuthToken()
  
  if (!token) {
    return null
  }

  return await verifyToken(token)
}

export async function isAuthenticated(): Promise<boolean> {
  const user = await getCurrentUser()
  return !!user && user.isAdmin
}