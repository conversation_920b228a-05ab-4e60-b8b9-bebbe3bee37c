// 测试数据库迁移结果
const mysql = require('mysql2/promise');

async function testMigration() {
  console.log('🚀 开始测试数据库迁移结果...\n');
  
  try {
    const connection = await mysql.createConnection({
      host: 'gateway01.ap-northeast-1.prod.aws.tidbcloud.com',
      port: 4000,
      user: '2oERGgZyGpnieY9.root',
      password: 'csMQAt3HshS7CVFH',
      database: 'blog_db',
      ssl: {
        rejectUnauthorized: false
      }
    });

    console.log('✅ 数据库连接成功！\n');
    
    // 测试1: 查看所有表
    console.log('📋 1. 检查数据库表结构:');
    const [tables] = await connection.execute('SHOW TABLES');
    console.log('   现有表:', tables.map(t => Object.values(t)[0]).join(', '));
    
    // 测试2: 查询文章数据
    console.log('\n📝 2. 查询文章数据:');
    const [posts] = await connection.execute('SELECT id, title, slug, published, is_featured FROM posts');
    console.log(`   共有 ${posts.length} 篇文章:`);
    posts.forEach(post => {
      console.log(`   - [${post.id}] ${post.title} (${post.slug}) - 发布:${post.published ? '是' : '否'}, 精选:${post.is_featured ? '是' : '否'}`);
    });
    
    // 测试3: 查询标签数据
    console.log('\n🏷️  3. 查询标签数据:');
    const [tags] = await connection.execute('SELECT id, name, slug FROM tags');
    console.log(`   共有 ${tags.length} 个标签:`);
    tags.forEach(tag => {
      console.log(`   - [${tag.id}] ${tag.name} (${tag.slug})`);
    });
    
    // 测试4: 查询文章标签关联
    console.log('\n🔗 4. 查询文章标签关联:');
    const [postTags] = await connection.execute(`
      SELECT pt.id, p.title, t.name as tag_name 
      FROM post_tags pt 
      JOIN posts p ON pt.post_id = p.id 
      JOIN tags t ON pt.tag_id = t.id
    `);
    console.log(`   共有 ${postTags.length} 个关联:`);
    postTags.forEach(pt => {
      console.log(`   - 文章"${pt.title}" 关联标签"${pt.tag_name}"`);
    });
    
    // 测试5: 测试复杂查询
    console.log('\n🔍 5. 测试复杂查询 - 获取已发布的文章及其标签:');
    const [publishedPosts] = await connection.execute(`
      SELECT 
        p.id, 
        p.title, 
        p.slug, 
        p.excerpt,
        p.published,
        p.created_at,
        GROUP_CONCAT(t.name) as tags
      FROM posts p
      LEFT JOIN post_tags pt ON p.id = pt.post_id
      LEFT JOIN tags t ON pt.tag_id = t.id
      WHERE p.published = true
      GROUP BY p.id, p.title, p.slug, p.excerpt, p.published, p.created_at
      ORDER BY p.created_at DESC
    `);
    
    console.log(`   查询到 ${publishedPosts.length} 篇已发布文章:`);
    publishedPosts.forEach(post => {
      console.log(`   - ${post.title}`);
      console.log(`     链接: /blog/${post.slug}`);
      console.log(`     摘要: ${post.excerpt || '无摘要'}`);
      console.log(`     标签: ${post.tags || '无标签'}`);
      console.log(`     发布时间: ${post.created_at}`);
      console.log('');
    });
    
    await connection.end();
    
    console.log('🎉 数据库迁移测试完成！');
    console.log('\n✅ 迁移结果总结:');
    console.log(`   - 数据库连接: 成功`);
    console.log(`   - 表结构: 正常 (${tables.length} 个表)`);
    console.log(`   - 文章数据: ${posts.length} 篇`);
    console.log(`   - 标签数据: ${tags.length} 个`);
    console.log(`   - 关联数据: ${postTags.length} 个`);
    console.log(`   - 已发布文章: ${publishedPosts.length} 篇`);
    console.log('\n🚀 项目已成功从PostgreSQL迁移到MySQL！');
    
  } catch (error) {
    console.error('❌ 测试失败:', error.message);
    process.exit(1);
  }
}

testMigration();
