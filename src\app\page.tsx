import { getPublishedPosts, getFeaturedPosts } from '@/lib/posts'
import { format } from 'date-fns'
import { zhCN } from 'date-fns/locale'
import Link from 'next/link'

export default async function Home() {
  // 并行获取精选文章和最新文章
  const [featuredPosts, latestPosts] = await Promise.all([
    getFeaturedPosts(4), // 限制精选文章数量
    getPublishedPosts(6)  // 限制最新文章数量
  ])

  return (
    <div className="container mx-auto px-4 py-16">
      {/* 头部介绍 */}
      <div className="text-center mb-16">
        <h1 className="text-6xl font-bold mb-6 text-white bg-gradient-to-r from-purple-400 via-pink-400 to-purple-600 bg-clip-text text-transparent">
          欢迎来到我的博客
        </h1>
        <p className="text-xl text-white/80 mb-8 max-w-2xl mx-auto leading-relaxed">
          在这里，我分享关于技术、思考和生活的点点滴滴。
          希望这些内容能对你有所帮助和启发。
        </p>
      </div>
      
      {/* 精选文章区域 */}
      {featuredPosts.length > 0 && (
        <div className="max-w-6xl mx-auto mb-20">
          <div className="text-center mb-12">
            <h2 className="text-4xl font-bold text-white mb-4">
              <span className="bg-gradient-to-r from-yellow-400 via-orange-400 to-red-400 bg-clip-text text-transparent">
                ✨ 精选文章
              </span>
            </h2>
            <p className="text-white/60 text-lg">精心挑选的优质内容</p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            {featuredPosts.map((post) => (
              <article 
                key={post.id} 
                className="group relative bg-gradient-to-br from-purple-500/10 via-pink-500/10 to-orange-500/10 backdrop-blur-sm rounded-3xl p-8 border-2 border-gradient-to-r from-purple-400/30 via-pink-400/30 to-orange-400/30 hover:border-yellow-400/60 hover:bg-gradient-to-br hover:from-purple-500/20 hover:via-pink-500/20 hover:to-orange-500/20 transition-all duration-500 hover:shadow-2xl hover:shadow-purple-500/30 hover:scale-105"
              >
                {/* 精选标识 */}
                <div className="absolute -top-4 -right-4 bg-gradient-to-r from-yellow-400 to-orange-500 text-white px-4 py-2 rounded-full text-sm font-bold shadow-lg">
                  <span className="flex items-center space-x-1">
                    <span>⭐</span>
                    <span>精选</span>
                  </span>
                </div>
                
                <h3 className="text-2xl font-bold mb-4 group-hover:text-yellow-300 transition-colors duration-300">
                  <Link href={`/blog/${post.slug}`} className="text-white hover:text-yellow-300 transition-colors">
                    {post.title}
                  </Link>
                </h3>
                
                {post.excerpt && (
                  <p className="text-white/80 mb-6 text-lg leading-relaxed">{post.excerpt}</p>
                )}
                
                <div className="flex items-center justify-between">
                  <div className="flex items-center text-sm text-white/60 space-x-4">
                    {post.createdAt && (
                      <time className="flex items-center space-x-2">
                        <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                          <path fillRule="evenodd" d="M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z" clipRule="evenodd" />
                        </svg>
                        <span>
                          {format(new Date(post.createdAt), 'yyyy年MM月dd日', { locale: zhCN })}
                        </span>
                      </time>
                    )}
                  </div>
                  <Link
                    href={`/blog/${post.slug}`}
                    className="inline-flex items-center space-x-2 bg-gradient-to-r from-yellow-500 to-orange-500 hover:from-yellow-600 hover:to-orange-600 text-white px-6 py-3 rounded-full transition-all duration-300 hover:shadow-lg hover:shadow-yellow-500/25 transform hover:scale-105 font-medium"
                  >
                    <span>阅读精选</span>
                    <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 7l5 5m0 0l-5 5m5-5H6" />
                    </svg>
                  </Link>
                </div>
              </article>
            ))}
          </div>
        </div>
      )}
      
      {/* 最新文章区域 */}
      <div className="max-w-4xl mx-auto">
        <h2 className="text-4xl font-bold mb-12 text-white text-center">最新文章</h2>
        <div className="space-y-8">
          {latestPosts.length === 0 ? (
            <div className="text-center py-20">
              <div className="bg-white/5 backdrop-blur-sm rounded-2xl p-12 border border-white/10">
                <p className="text-white/60 text-lg">暂无文章，请先添加一些内容。</p>
              </div>
            </div>
          ) : (
            latestPosts.map((post) => (
              <article 
                key={post.id} 
                className="group bg-white/5 backdrop-blur-sm rounded-2xl p-8 border border-white/10 hover:border-purple-400/50 hover:bg-white/10 transition-all duration-500 hover:shadow-2xl hover:shadow-purple-500/20"
              >
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <h3 className="text-2xl font-bold mb-4 group-hover:text-purple-300 transition-colors duration-300">
                      <Link href={`/blog/${post.slug}`} className="text-white hover:text-purple-300 transition-colors">
                        {post.title}
                      </Link>
                    </h3>
                    {post.excerpt && (
                      <p className="text-white/70 mb-6 text-lg leading-relaxed">{post.excerpt}</p>
                    )}
                    <div className="flex items-center text-sm text-white/50 space-x-4">
                      {post.createdAt && (
                        <time className="flex items-center space-x-2">
                          <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                            <path fillRule="evenodd" d="M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z" clipRule="evenodd" />
                          </svg>
                          <span>
                            {format(new Date(post.createdAt), 'yyyy年MM月dd日', { locale: zhCN })}
                          </span>
                        </time>
                      )}
                      <span className="flex items-center space-x-2">
                        <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                          <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clipRule="evenodd" />
                        </svg>
                        <span>5 分钟阅读</span>
                      </span>
                    </div>
                  </div>
                  
                  <div className="ml-6">
                    <Link
                      href={`/blog/${post.slug}`}
                      className="inline-flex items-center space-x-2 bg-gradient-to-r from-purple-500 to-pink-500 hover:from-purple-600 hover:to-pink-600 text-white px-6 py-3 rounded-full transition-all duration-300 hover:shadow-lg hover:shadow-purple-500/25 transform hover:scale-105"
                    >
                      <span className="font-medium">阅读全文</span>
                      <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 7l5 5m0 0l-5 5m5-5H6" />
                      </svg>
                    </Link>
                  </div>
                </div>
              </article>
            ))
          )}
        </div>
        
        {/* 查看更多按钮 */}
        {latestPosts.length > 0 && (
          <div className="text-center mt-16">
            <Link
              href="/blog"
              className="inline-flex items-center space-x-3 bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700 text-white px-10 py-4 rounded-full transition-all duration-300 hover:shadow-xl hover:shadow-purple-500/30 transform hover:scale-105 text-lg font-semibold"
            >
              <span>查看所有文章</span>
              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
              </svg>
            </Link>
          </div>
        )}
      </div>
    </div>
  );
}