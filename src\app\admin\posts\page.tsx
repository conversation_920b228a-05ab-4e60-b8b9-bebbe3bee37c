import { Suspense } from 'react'
import Link from 'next/link'
import { 
  getAllPostsForAdmin, 
  type Post 
} from '@/lib/posts'
import { 
  togglePostStatusAction, 
  togglePostFeaturedAction 
} from '@/lib/actions'
import { format } from 'date-fns'
import DeleteButton from '@/components/admin/DeleteButton'

interface PostsPageProps {
  searchParams: {
    page?: string
    search?: string
    status?: 'all' | 'published' | 'draft' | 'featured'
  }
}

export default async function PostsPage({ searchParams }: PostsPageProps) {
  const page = Number(searchParams.page) || 1
  const search = searchParams.search || ''
  const status = searchParams.status || 'all'

  const { posts, total, totalPages } = await getAllPostsForAdmin(
    page,
    10,
    search,
    status
  )

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
        <div className="px-4 py-6 sm:px-0">
          {/* 页面标题和操作 */}
          <div className="sm:flex sm:items-center sm:justify-between">
            <div>
              <h1 className="text-2xl font-bold text-gray-900">文章管理</h1>
              <p className="mt-2 text-sm text-gray-700">
                共 {total} 篇文章
              </p>
            </div>
            <div className="mt-4 sm:mt-0">
              <Link
                href="/admin/posts/new"
                className="inline-flex items-center justify-center rounded-md border border-transparent bg-indigo-600 px-4 py-2 text-sm font-medium text-white shadow-sm hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2"
              >
                创建文章
              </Link>
            </div>
          </div>

          {/* 搜索和过滤 */}
          <div className="mt-6 bg-white shadow rounded-lg">
            <div className="px-4 py-4 border-b border-gray-200">
              <div className="sm:flex sm:items-center sm:justify-between">
                <div className="flex-1 min-w-0">
                  <form method="GET" className="sm:flex sm:items-center">
                    <div className="w-full sm:max-w-xs">
                      <label htmlFor="search" className="sr-only">
                        搜索文章
                      </label>
                      <input
                        type="text"
                        name="search"
                        id="search"
                        defaultValue={search}
                        className="block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                        placeholder="搜索文章标题或内容..."
                      />
                    </div>
                    <div className="mt-3 sm:mt-0 sm:ml-4">
                      <select
                        name="status"
                        defaultValue={status}
                        className="block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                      >
                        <option value="all">全部状态</option>
                        <option value="published">已发布</option>
                        <option value="draft">草稿</option>
                        <option value="featured">精选</option>
                      </select>
                    </div>
                    <div className="mt-3 sm:mt-0 sm:ml-4">
                      <button
                        type="submit"
                        className="inline-flex items-center justify-center rounded-md border border-transparent bg-indigo-600 px-4 py-2 text-sm font-medium text-white shadow-sm hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2"
                      >
                        搜索
                      </button>
                    </div>
                  </form>
                </div>
              </div>
            </div>

            {/* 文章列表 */}
            <div className="overflow-hidden">
              <Suspense fallback={<div className="p-4">加载中...</div>}>
                <PostsList posts={posts} />
              </Suspense>
            </div>

            {/* 分页 */}
            {totalPages > 1 && (
              <div className="bg-white px-4 py-3 border-t border-gray-200 sm:px-6">
                <div className="flex items-center justify-between">
                  <div className="flex-1 flex justify-between sm:hidden">
                    {page > 1 && (
                      <Link
                        href={`?page=${page - 1}&search=${search}&status=${status}`}
                        className="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"
                      >
                        上一页
                      </Link>
                    )}
                    {page < totalPages && (
                      <Link
                        href={`?page=${page + 1}&search=${search}&status=${status}`}
                        className="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"
                      >
                        下一页
                      </Link>
                    )}
                  </div>
                  <div className="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
                    <div>
                      <p className="text-sm text-gray-700">
                        显示第 <span className="font-medium">{(page - 1) * 10 + 1}</span> 到{' '}
                        <span className="font-medium">
                          {Math.min(page * 10, total)}
                        </span>{' '}
                        条，共 <span className="font-medium">{total}</span> 条记录
                      </p>
                    </div>
                    <div>
                      <nav className="relative z-0 inline-flex rounded-md shadow-sm -space-x-px">
                        {page > 1 && (
                          <Link
                            href={`?page=${page - 1}&search=${search}&status=${status}`}
                            className="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50"
                          >
                            上一页
                          </Link>
                        )}
                        
                        {/* 页码 */}
                        {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                          let pageNum
                          if (totalPages <= 5) {
                            pageNum = i + 1
                          } else if (page <= 3) {
                            pageNum = i + 1
                          } else if (page >= totalPages - 2) {
                            pageNum = totalPages - 4 + i
                          } else {
                            pageNum = page - 2 + i
                          }
                          
                          return (
                            <Link
                              key={pageNum}
                              href={`?page=${pageNum}&search=${search}&status=${status}`}
                              className={`relative inline-flex items-center px-4 py-2 border text-sm font-medium ${
                                pageNum === page
                                  ? 'z-10 bg-indigo-50 border-indigo-500 text-indigo-600'
                                  : 'bg-white border-gray-300 text-gray-500 hover:bg-gray-50'
                              }`}
                            >
                              {pageNum}
                            </Link>
                          )
                        })}
                        
                        {page < totalPages && (
                          <Link
                            href={`?page=${page + 1}&search=${search}&status=${status}`}
                            className="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50"
                          >
                            下一页
                          </Link>
                        )}
                      </nav>
                    </div>
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  )
}

// 文章列表组件
function PostsList({ posts }: { posts: Post[] }) {
  if (posts.length === 0) {
    return (
      <div className="text-center py-12">
        <svg
          className="mx-auto h-12 w-12 text-gray-400"
          fill="none"
          viewBox="0 0 24 24"
          stroke="currentColor"
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2}
            d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
          />
        </svg>
        <h3 className="mt-2 text-sm font-medium text-gray-900">暂无文章</h3>
        <p className="mt-1 text-sm text-gray-500">开始创建你的第一篇文章吧。</p>
        <div className="mt-6">
          <Link
            href="/admin/posts/new"
            className="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
          >
            创建文章
          </Link>
        </div>
      </div>
    )
  }

  return (
    <div className="overflow-x-auto">
      <table className="min-w-full divide-y divide-gray-200">
        <thead className="bg-gray-50">
          <tr>
            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              标题
            </th>
            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              状态
            </th>
            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              创建时间
            </th>
            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              更新时间
            </th>
            <th className="relative px-6 py-3">
              <span className="sr-only">操作</span>
            </th>
          </tr>
        </thead>
        <tbody className="bg-white divide-y divide-gray-200">
          {posts.map((post) => (
            <PostRow key={post.id} post={post} />
          ))}
        </tbody>
      </table>
    </div>
  )
}

// 文章行组件
function PostRow({ post }: { post: Post }) {
  return (
    <tr>
      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
        <div className="flex items-center">
          <div>
            <div className="font-medium text-gray-900">{post.title}</div>
            <div className="text-gray-500 text-xs">/{post.slug}</div>
            {post.isFeatured && (
              <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800 mt-1">
                精选
              </span>
            )}
          </div>
        </div>
      </td>
      <td className="px-6 py-4 whitespace-nowrap">
        <span
          className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
            post.published
              ? 'bg-green-100 text-green-800'
              : 'bg-yellow-100 text-yellow-800'
          }`}
        >
          {post.published ? '已发布' : '草稿'}
        </span>
      </td>
      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
        {post.createdAt ? format(new Date(post.createdAt), 'yyyy-MM-dd HH:mm') : '-'}
      </td>
      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
        {post.updatedAt ? format(new Date(post.updatedAt), 'yyyy-MM-dd HH:mm') : '-'}
      </td>
      <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
        <div className="flex items-center justify-end space-x-2">
          <Link
            href={`/admin/posts/${post.id}/edit`}
            className="text-indigo-600 hover:text-indigo-900"
          >
            编辑
          </Link>
          <form action={togglePostStatusAction.bind(null, post.id)} className="inline">
            <button
              type="submit"
              className="text-blue-600 hover:text-blue-900"
            >
              {post.published ? '取消发布' : '发布'}
            </button>
          </form>
          <form action={togglePostFeaturedAction.bind(null, post.id)} className="inline">
            <button
              type="submit"
              className="text-orange-600 hover:text-orange-900"
            >
              {post.isFeatured ? '取消精选' : '设为精选'}
            </button>
          </form>
          <DeleteButton postId={post.id} postTitle={post.title} />
        </div>
      </td>
    </tr>
  )
}