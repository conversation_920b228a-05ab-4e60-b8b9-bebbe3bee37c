'use client'

import { useTheme } from '@/contexts/theme-context'

export default function ThemeToggle() {
  const { theme, toggleTheme, isDarkMode } = useTheme()

  return (
    <button
      onClick={toggleTheme}
      className={`
        relative p-2 rounded-full transition-all duration-300 ease-in-out
        ${isDarkMode 
          ? 'bg-gray-800/80 text-yellow-400 hover:bg-gray-700/80' 
          : 'bg-white/80 text-gray-800 hover:bg-white/90'
        }
        backdrop-blur-sm shadow-lg hover:shadow-xl
        border ${isDarkMode ? 'border-gray-700' : 'border-gray-200'}
        hover:scale-110 active:scale-95
      `}
      aria-label="切换主题"
      title={isDarkMode ? '切换到浅色模式' : '切换到深色模式'}
    >
      <div className="relative w-6 h-6 flex items-center justify-center">
        {/* 太阳图标 */}
        <svg
          className={`absolute w-5 h-5 transition-all duration-300 ${
            isDarkMode 
              ? 'opacity-0 rotate-90 scale-0' 
              : 'opacity-100 rotate-0 scale-100'
          }`}
          fill="currentColor"
          viewBox="0 0 20 20"
        >
          <path
            fillRule="evenodd"
            d="M10 2a1 1 0 011 1v1a1 1 0 11-2 0V3a1 1 0 011-1zm4 8a4 4 0 11-8 0 4 4 0 018 0zm-.464 4.95l.707.707a1 1 0 001.414-1.414l-.707-.707a1 1 0 00-1.414 1.414zm2.12-10.607a1 1 0 010 1.414l-.706.707a1 1 0 11-1.414-1.414l.707-.707a1 1 0 011.414 0zM17 11a1 1 0 100-2h-1a1 1 0 100 2h1zm-7 4a1 1 0 011 1v1a1 1 0 11-2 0v-1a1 1 0 011-1zM5.05 6.464A1 1 0 106.465 5.05l-.708-.707a1 1 0 00-1.414 1.414l.707.707zm1.414 8.486l-.707.707a1 1 0 01-1.414-1.414l.707-.707a1 1 0 011.414 1.414zM4 11a1 1 0 100-2H3a1 1 0 000 2h1z"
            clipRule="evenodd"
          />
        </svg>
        
        {/* 月亮图标 */}
        <svg
          className={`absolute w-5 h-5 transition-all duration-300 ${
            isDarkMode 
              ? 'opacity-100 rotate-0 scale-100' 
              : 'opacity-0 -rotate-90 scale-0'
          }`}
          fill="currentColor"
          viewBox="0 0 20 20"
        >
          <path d="M17.293 13.293A8 8 0 016.707 2.707a8.001 8.001 0 1010.586 10.586z" />
        </svg>
      </div>
    </button>
  )
}