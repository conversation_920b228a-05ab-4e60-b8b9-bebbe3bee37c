import { z } from 'zod'

// 文章表单验证模式
export const postSchema = z.object({
  title: z
    .string()
    .min(1, '标题不能为空')
    .max(200, '标题长度不能超过200个字符'),
  
  slug: z
    .string()
    .min(1, 'URL别名不能为空')
    .max(100, 'URL别名长度不能超过100个字符')
    .regex(/^[a-z0-9-]+$/, 'URL别名只能包含小写字母、数字和连字符'),
  
  content: z
    .string()
    .min(1, '内容不能为空'),
  
  excerpt: z
    .string()
    .max(500, '摘要长度不能超过500个字符')
    .optional(),
  
  published: z
    .boolean()
    .default(false),
  
  isFeatured: z
    .boolean()
    .default(false),
  
  tagIds: z
    .array(z.number())
    .optional()
    .default([])
})

// 文章创建表单类型
export type PostFormData = z.infer<typeof postSchema>

// 标签表单验证模式
export const tagSchema = z.object({
  name: z
    .string()
    .min(1, '标签名称不能为空')
    .max(50, '标签名称长度不能超过50个字符'),
  
  slug: z
    .string()
    .min(1, 'URL别名不能为空')
    .max(50, 'URL别名长度不能超过50个字符')
    .regex(/^[a-z0-9-]+$/, 'URL别名只能包含小写字母、数字和连字符')
})

// 标签表单类型
export type TagFormData = z.infer<typeof tagSchema>

// 搜索和筛选表单验证模式
export const postsFilterSchema = z.object({
  page: z
    .number()
    .min(1, '页码必须大于0')
    .default(1),
  
  limit: z
    .number()
    .min(1, '每页数量必须大于0')
    .max(100, '每页数量不能超过100')
    .default(10),
  
  search: z
    .string()
    .max(100, '搜索关键词长度不能超过100个字符')
    .optional(),
  
  status: z
    .enum(['all', 'published', 'draft', 'featured'])
    .default('all')
})

// 筛选表单类型
export type PostsFilterData = z.infer<typeof postsFilterSchema>

// 文章操作验证模式
export const postActionSchema = z.object({
  id: z
    .number()
    .min(1, '文章ID无效')
})

// 批量操作验证模式
export const batchActionSchema = z.object({
  ids: z
    .array(z.number().min(1))
    .min(1, '请选择至少一篇文章'),
  
  action: z
    .enum(['delete', 'publish', 'unpublish', 'feature', 'unfeature'])
})

// 批量操作类型
export type BatchActionData = z.infer<typeof batchActionSchema>

// Server Action 返回结果类型
export interface ActionResult<T = unknown> {
  success: boolean
  data?: T
  error?: string
  errors?: Record<string, string[]>
}

// 分页结果类型
export interface PaginationInfo {
  page: number
  limit: number
  total: number
  totalPages: number
  hasNext: boolean
  hasPrev: boolean
}