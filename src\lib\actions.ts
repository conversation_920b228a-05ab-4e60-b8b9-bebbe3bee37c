'use server'

import { redirect } from 'next/navigation'
import { revalidatePath } from 'next/cache'
import { 
  validateCredentials, 
  generateToken, 
  setAuthCookie, 
  clearAuthCookie,

  type LoginCredentials 
} from './auth'
import {
  createPost,
  updatePost,
  deletePost,
  togglePostStatus,
  togglePostFeatured,
  generateSlug,
  isSlugExists,
  updatePostTags,
  createTag,
  getAllTags
} from './posts'
import { 
  postSchema,
  tagSchema,
  type PostFormData,
  type TagFormData,
  type ActionResult 
} from './schemas'

export interface LoginResult {
  success: boolean
  error?: string
}

// 认证相关 Actions
export async function login(credentials: LoginCredentials): Promise<LoginResult> {
  try {
    const isValid = await validateCredentials(credentials)
    
    if (!isValid) {
      return {
        success: false,
        error: '用户名或密码错误'
      }
    }

    const token = await generateToken(credentials.username)
    await setAuthCookie(token)

    return {
      success: true
    }
  } catch (error) {
    console.error('登录失败:', error)
    return {
      success: false,
      error: '登录过程中发生错误，请重试'
    }
  }
}

export async function logout(): Promise<void> {
  await clearAuthCookie()
  redirect('/admin/login')
}

// 文章相关 Actions

export async function createPostAction(formData: FormData): Promise<ActionResult> {
  try {
    // 提取表单数据
    const rawData = {
      title: formData.get('title') as string,
      slug: formData.get('slug') as string,
      content: formData.get('content') as string,
      excerpt: formData.get('excerpt') as string || undefined,
      published: formData.get('published') === 'true',
      isFeatured: formData.get('isFeatured') === 'true',
      tagIds: JSON.parse(formData.get('tagIds') as string || '[]')
    }

    // 验证数据
    const validatedData = postSchema.parse(rawData)

    // 检查slug是否已存在
    if (await isSlugExists(validatedData.slug)) {
      return {
        success: false,
        error: 'URL别名已存在，请使用其他别名'
      }
    }

    // 创建文章
    const post = await createPost({
      title: validatedData.title,
      slug: validatedData.slug,
      content: validatedData.content,
      excerpt: validatedData.excerpt,
      published: validatedData.published,
      isFeatured: validatedData.isFeatured
    })

    if (!post) {
      return {
        success: false,
        error: '创建文章失败'
      }
    }

    // 更新标签关联
    if (validatedData.tagIds && validatedData.tagIds.length > 0) {
      await updatePostTags(post.id, validatedData.tagIds)
    }

    // 重新验证相关页面
    revalidatePath('/admin/posts')
    
    return {
      success: true,
      data: post
    }
  } catch (error: any) {
    console.error('创建文章失败:', error)
    
    if (error.name === 'ZodError') {
      return {
        success: false,
        error: '表单数据验证失败',
        errors: error.flatten().fieldErrors
      }
    }

    return {
      success: false,
      error: '创建文章时发生错误'
    }
  }
}

export async function updatePostAction(id: number, formData: FormData): Promise<ActionResult> {
  try {
    // 提取表单数据
    const rawData = {
      title: formData.get('title') as string,
      slug: formData.get('slug') as string,
      content: formData.get('content') as string,
      excerpt: formData.get('excerpt') as string || undefined,
      published: formData.get('published') === 'true',
      isFeatured: formData.get('isFeatured') === 'true',
      tagIds: JSON.parse(formData.get('tagIds') as string || '[]')
    }

    // 验证数据
    const validatedData = postSchema.parse(rawData)

    // 检查slug是否已存在（排除当前文章）
    if (await isSlugExists(validatedData.slug, id)) {
      return {
        success: false,
        error: 'URL别名已存在，请使用其他别名'
      }
    }

    // 更新文章
    const post = await updatePost(id, {
      title: validatedData.title,
      slug: validatedData.slug,
      content: validatedData.content,
      excerpt: validatedData.excerpt,
      published: validatedData.published,
      isFeatured: validatedData.isFeatured
    })

    if (!post) {
      return {
        success: false,
        error: '更新文章失败'
      }
    }

    // 更新标签关联
    await updatePostTags(post.id, validatedData.tagIds || [])

    // 重新验证相关页面
    revalidatePath('/admin/posts')
    revalidatePath(`/admin/posts/${id}/edit`)
    
    return {
      success: true,
      data: post
    }
  } catch (error: any) {
    console.error('更新文章失败:', error)
    
    if (error.name === 'ZodError') {
      return {
        success: false,
        error: '表单数据验证失败',
        errors: error.flatten().fieldErrors
      }
    }

    return {
      success: false,
      error: '更新文章时发生错误'
    }
  }
}

export async function deletePostAction(id: number): Promise<ActionResult> {
  try {
    const success = await deletePost(id)
    
    if (!success) {
      return {
        success: false,
        error: '删除文章失败'
      }
    }

    // 重新验证相关页面
    revalidatePath('/admin/posts')
    
    return {
      success: true
    }
  } catch (error) {
    console.error('删除文章失败:', error)
    return {
      success: false,
      error: '删除文章时发生错误'
    }
  }
}

export async function deletePostWithConfirmAction(id: number): Promise<void> {
  'use server'
  
  try {
    const success = await deletePost(id)
    
    if (success) {
      // 重新验证相关页面
      revalidatePath('/admin/posts')
    }
  } catch (error) {
    console.error('删除文章失败:', error)
  }
}

export async function togglePostStatusAction(id: number): Promise<ActionResult> {
  try {
    const post = await togglePostStatus(id)
    
    if (!post) {
      return {
        success: false,
        error: '切换文章状态失败'
      }
    }

    // 重新验证相关页面
    revalidatePath('/admin/posts')
    
    return {
      success: true,
      data: post
    }
  } catch (error) {
    console.error('切换文章状态失败:', error)
    return {
      success: false,
      error: '切换文章状态时发生错误'
    }
  }
}

export async function togglePostFeaturedAction(id: number): Promise<ActionResult> {
  try {
    const post = await togglePostFeatured(id)
    
    if (!post) {
      return {
        success: false,
        error: '切换文章精选状态失败'
      }
    }

    // 重新验证相关页面
    revalidatePath('/admin/posts')
    
    return {
      success: true,
      data: post
    }
  } catch (error) {
    console.error('切换文章精选状态失败:', error)
    return {
      success: false,
      error: '切换文章精选状态时发生错误'
    }
  }
}

export async function generateSlugAction(title: string): Promise<ActionResult<string>> {
  try {
    if (!title) {
      return {
        success: false,
        error: '标题不能为空'
      }
    }

    let slug = generateSlug(title)
    let counter = 1

    // 如果slug已存在，添加数字后缀
    while (await isSlugExists(slug)) {
      const baseSlug = generateSlug(title)
      slug = `${baseSlug}-${counter}`
      counter++
    }

    return {
      success: true,
      data: slug
    }
  } catch (error) {
    console.error('生成slug失败:', error)
    return {
      success: false,
      error: '生成URL别名时发生错误'
    }
  }
}

// 标签相关 Actions

export async function createTagAction(formData: FormData): Promise<ActionResult> {
  try {
    const rawData = {
      name: formData.get('name') as string,
      slug: formData.get('slug') as string
    }

    // 验证数据
    const validatedData = tagSchema.parse(rawData)

    // 创建标签
    const tag = await createTag(validatedData)

    if (!tag) {
      return {
        success: false,
        error: '创建标签失败'
      }
    }

    return {
      success: true,
      data: tag
    }
  } catch (error: any) {
    console.error('创建标签失败:', error)
    
    if (error.name === 'ZodError') {
      return {
        success: false,
        error: '表单数据验证失败',
        errors: error.flatten().fieldErrors
      }
    }

    return {
      success: false,
      error: '创建标签时发生错误'
    }
  }
}