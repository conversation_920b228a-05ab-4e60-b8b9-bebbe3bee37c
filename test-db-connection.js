// 测试数据库连接
const mysql = require('mysql2/promise');

async function testConnection() {
  console.log('开始测试MySQL数据库连接...');
  
  try {
    const connection = await mysql.createConnection({
      host: 'gateway01.ap-northeast-1.prod.aws.tidbcloud.com',
      port: 4000,
      user: '2oERGgZyGpnieY9.root',
      password: 'csMQAt3HshS7CVFH',
      database: 'blog_db',
      ssl: {
        rejectUnauthorized: false
      }
    });

    console.log('✅ 数据库连接成功！');
    
    // 测试基本查询
    const [rows] = await connection.execute('SELECT 1 as test');
    console.log('✅ 基本查询测试成功:', rows);
    
    // 测试数据库信息
    const [dbInfo] = await connection.execute('SELECT DATABASE() as current_db, VERSION() as version');
    console.log('✅ 数据库信息:', dbInfo);
    
    await connection.end();
    console.log('✅ 连接已关闭');
    
  } catch (error) {
    console.error('❌ 数据库连接失败:', error.message);
    process.exit(1);
  }
}

testConnection();
